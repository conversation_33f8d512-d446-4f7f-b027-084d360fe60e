import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/deviceOnoff',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/deviceOnoff/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/deviceOnoff',
    method: 'put',
    data
  })
}

export function getALLDeviceOnoff(data) {
  return request({
    url: 'api/deviceOnoff/getALLDeviceOnoff',
    method: 'post',
    data
  })
}
export default { add, edit, del,getALLDeviceOnoff }
