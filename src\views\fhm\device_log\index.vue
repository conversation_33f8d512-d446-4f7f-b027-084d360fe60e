<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <!--      <label class="el-form-item-label">id</label>
              <el-input v-model="query.id" clearable placeholder="id" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">设备编id</label>
              <el-input v-model="query.deviceIp" clearable placeholder="设备编id" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">设备状态：1在线，2离线</label>
              <el-input v-model="query.deviceState" clearable placeholder="设备状态：1在线，2离线" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">网络状态：1稳定，2基本稳定，3轻微不稳定，4:严重不稳定</label>
              <el-input v-model="query.networkState" clearable placeholder="网络状态：1稳定，2基本稳定，3轻微不稳定，4:严重不稳定" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">删除状态[0：未删除；1：已删除]</label>
              <el-input v-model="query.deleted" clearable placeholder="删除状态[0：未删除；1：已删除]" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">创建时间</label>
              <el-input v-model="query.createTime" clearable placeholder="创建时间" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">详细记录</label>
              <el-input v-model="query.outputLog" clearable placeholder="详细记录" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">丢包率</label>
              <el-input v-model="query.lossRate" clearable placeholder="丢包率" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">尝试次数</label>
              <el-input v-model="query.attempts" clearable placeholder="尝试次数" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">成功率</label>
              <el-input v-model="query.successRate" clearable placeholder="成功率" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
              <label class="el-form-item-label">成功次数</label>
              <el-input v-model="query.frequency" clearable placeholder="成功次数" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />-->


        <label class="el-form-item-label">设备IP</label>
        <el-input v-model="query.deviceIp" clearable placeholder="设备ip" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">设备编号</label>
        <el-input v-model="query.deviceCode" clearable placeholder="设备编号" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">位置</label>
        <el-input v-model="query.devicePosition" clearable placeholder="位置" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>


        <label class="el-form-item-label">设备状态</label>


        <el-select
          v-model="query.deviceState"
          placeholder="请选择"
          style="width: 185px;" class="filter-item"
        >
          <el-option
            v-for="item in deviceStateList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>


        <label class="el-form-item-label">网络状态</label>
        <el-select
          v-model="query.networkState"
          placeholder="请选择"
          style="width: 185px;" class="filter-item"
        >
          <el-option
            v-for="item in networkList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>


        <rrOperation :crud="crud"/>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission"/>
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0"
                 :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="id" prop="id">
            <el-input v-model="form.id" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="设备编id">
            <el-input v-model="form.deviceIp" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="设备状态：1在线，2离线">
            <el-input v-model="form.deviceState" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="网络状态：1稳定，2基本稳定，3轻微不稳定，4:严重不稳定">
            <el-input v-model="form.networkState" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="删除状态[0：未删除；1：已删除]" prop="deleted">
            <el-input v-model="form.deleted" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createTime" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="详细记录">
            <el-input v-model="form.outputLog" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="丢包率">
            <el-input v-model="form.lossRate" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="尝试次数">
            <el-input v-model="form.attempts" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="成功率">
            <el-input v-model="form.successRate" style="width: 370px;"/>
          </el-form-item>
          <el-form-item label="成功次数">
            <el-input v-model="form.frequency" style="width: 370px;"/>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;"
                @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55"/>
        <el-table-column prop="deviceIp" label="设备IP"/>
        <el-table-column prop="deviceState" label="设备状态">
          <template slot-scope="scope">
          <span :class="getStatusColor(scope.row.deviceState)">
            {{ dict.label.device_state[scope.row.deviceState] }}
          </span>
          </template>
        </el-table-column>
        <el-table-column prop="networkState" label="网络状态">
          <template slot-scope="scope">

            <span :class="getNetworkStateColor(scope.row.networkState)">
            {{ dict.label.network_state[scope.row.networkState] }}
          </span>
          </template>
        </el-table-column>


        <el-table-column prop="outputLog" label="详细记录" show-overflow-tooltip>
          <template slot-scope="scope">

            <el-button @click="info(scope.row.outputLog)" size="mini" style="width: 130px;overflow: hidden">
              {{ scope.row.outputLog}}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="lossRate" label="丢包率"/>
        <el-table-column prop="attempts" label="尝试次数"/>
        <el-table-column prop="successRate" label="成功率"/>
        <el-table-column prop="frequency" label="成功次数"/>
        <el-table-column prop="createTime" label="创建时间"/>


      </el-table>
      <!--分页组件-->
      <pagination/>

      <el-dialog :visible.sync="dialog" title="详细记录" append-to-body top="10%" width="550px">
        <pre style="padding: 20px">{{ outputLog }}</pre>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import crudDeviceLineLog from '@/api/fhm/deviceLineLog'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import {findByType} from "@/api/system/dictDetail";
const defaultForm = { id: null, deviceIp: null, deviceState: null, networkState: null, deleted: null, createTime: null, outputLog: null, lossRate: null, attempts: null, successRate: null, frequency: null }
export default {
  name: 'DeviceLineLog',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '防护门装置在线离线日志', url: 'api/deviceLineLog', idField: 'id', sort: 'id,desc', crudMethod: { ...crudDeviceLineLog }, optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true
      }})
  },
  // 数据字典
  dicts: ['network_state', 'device_state'],
  data() {
    return {
      outputLog: '',
      dialog: false,
      networkList: [],
      deviceStateList: [],
      permission: {
        add: ['admin', 'deviceLineLog:add'],
        edit: ['admin', 'deviceLineLog:edit'],
        del: ['admin', 'deviceLineLog:del']
      },
      rules: {
        id: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        deviceIp: [
          { required: true, message: '设备编id不能为空', trigger: 'blur' }
        ],
        deviceState: [
          { required: true, message: '设备状态：1在线，2离线不能为空', trigger: 'blur' }
        ],
        networkState: [
          { required: true, message: '网络状态：1稳定，2基本稳定，3轻微不稳定，4:严重不稳定不能为空', trigger: 'blur' }
        ],
        lossRate: [
          { required: true, message: '丢包率不能为空', trigger: 'blur' }
        ],
        attempts: [
          { required: true, message: '尝试次数不能为空', trigger: 'blur' }
        ],
        successRate: [
          { required: true, message: '成功率不能为空', trigger: 'blur' }
        ],
        frequency: [
          { required: true, message: '成功次数不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'id', display_name: 'id' },
        { key: 'deviceIp', display_name: '设备编id' },
        { key: 'deviceState', display_name: '设备状态：1在线，2离线' },
        { key: 'networkState', display_name: '网络状态：1稳定，2基本稳定，3轻微不稳定，4:严重不稳定' },
        { key: 'lossRate', display_name: '丢包率' },
        { key: 'attempts', display_name: '尝试次数' },
        { key: 'successRate', display_name: '成功率' },
        { key: 'frequency', display_name: '成功次数' }
      ]
    }
  },
  mounted() {

    this.findByTypeNetworkList();
    this.findByTypeDeviceStateList();
  },
  methods: {

    // 获取异常详情
    info(val) {
      this.dialog = true
      this.outputLog = val
    },

    getStatusColor(state) {


      // 根据设备状态返回对应的颜色类
      // 这里需要根据你的实际状态值和颜色需求进行调整
      switch (state) {
        case '1': // 假设在线状态值为'online'
          return 'text-green';
        case '2': // 假设离线状态值为'offline'
          return 'text-red';
      }
    },
    getNetworkStateColor(state) {
      // 根据设备状态返回对应的颜色类
      // 这里需要根据你的实际状态值和颜色需求进行调整
      switch (state) {
        case '1': //
          return 'text-green';
        case '2': //
          return 'text-green';
        case '3': //
          return 'text-yellow';
        case '4': //
          return 'text-red';
      }
    },
    //通信状态
    findByTypeNetworkList() {
      const param = {
        dictName: "network_state",
      };
      findByType(param).then((res) => {
        this.networkList = res;
      });
    },
    findByTypeDeviceStateList() {
      const param = {
        dictName: "device_state",
      };
      findByType(param).then((res) => {
        this.deviceStateList = res;
      });
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {

      var deviceIp = this.$route.query.deviceIp;  // 使用query方式
      this.query.deviceIp = deviceIp
      return true
    }
  }
}
</script>

<style scoped>
  .text-green {
    color: green;
  }

  .text-red {
    color: red;
  }

  .text-yellow {
    color: yellow;
  }

</style>
