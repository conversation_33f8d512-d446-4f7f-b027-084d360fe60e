<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->

        <label class="el-form-item-label">所属开关</label>

        <el-select

          placeholder="请选择"
          style="width: 150px"
          v-model="query.onoffId"
        >
          <el-option
            v-for="item in deviceOnoffList"
            :key="item.deviceIp"
            :label="item.deviceIp"
            :value="item.deviceId"
          >
          </el-option>
        </el-select>
        <label class="el-form-item-label">设备ip</label>
        <el-input v-model="query.deviceIp" clearable placeholder="设备ip" style="width: 100px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">设备编号</label>
        <el-input v-model="query.deviceCode" clearable placeholder="设备编号" style="width: 100px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">横通道</label>

        <el-select
          v-model="query.deviceType"
          placeholder="请选择"
          style="width: 150px"
        >
          <el-option
            v-for="item in rescueCrossChannel"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>

        <label class="el-form-item-label">位置</label>
        <el-input v-model="query.devicePosition" clearable placeholder="位置" style="width: 100px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">上下行</label>

        <el-select
          v-model="query.direction"
          placeholder="请选择"
          style="width: 100px"
        >
          <el-option
            v-for="item in upAnDownList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>

        <label class="el-form-item-label">拍照位置</label>

        <el-select
          v-model="query.photoLocation"
          placeholder="请选择"
          style="width: 100px"
        >
          <el-option
            v-for="item in photoLocationList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>


        <rrOperation :crud="crud"/>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission"/>
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0"
                 :title="crud.status.title" width="800px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="所属开关:"
                class="formItem"
                prop="onoffId"
              >
                <el-select
                  v-model="form.onoffId"
                  placeholder="请选择"
                  style="width: 200px"
                >
                  <el-option
                    v-for="item in deviceOnoffList"
                    :key="item.deviceIp"
                    :label="item.deviceIp"
                    :value="item.deviceId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备ip" prop="deviceIp">
                <el-input v-model="form.deviceIp" @change="validateIP" style="width: 200px;" maxlength="20"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备编号">
                <el-input v-model="form.deviceCode" style="width: 200px;"/>
              </el-form-item>
            </el-col>

          <el-col :span="12">

            <el-form-item

              label="横通道:"
              class="formItem"
              prop="deviceType"
            >
              <el-select
                v-model="form.deviceType"
                placeholder="请选择"
                style="width: 200px"
              >
                <el-option
                  v-for="item in rescueCrossChannel"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>


          </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="位置"              prop="devicePosition">
                <el-input v-model="form.devicePosition" style="width: 200px;"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">


              <el-form-item

                label="上下行:"
                class="formItem"
                prop="direction"
              >
                <el-select
                  v-model="form.direction"
                  placeholder="请选择"
                  style="width: 200px"
                >
                  <el-option
                    v-for="item in upAnDownList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

            </el-col>
          </el-row>


          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备状态:" class="formItem" prop="deviceState">
                <el-select v-model="form.deviceState" placeholder="请选择" style="width: 200px">
                  <el-option v-for="item in deviceStateList" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="网络状态:" class="formItem" prop="networkState">
                <el-select v-model="form.networkState" placeholder="请选择" style="width: 200px">
                  <el-option v-for="item in networkList" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="环形传感器状态">
                <el-select v-model="form.lockingSensorStatus" placeholder="请选择" style="width: 200px">
                  <el-option v-for="item in sensorStatusList" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>


              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="门磁传感器状态">

                <el-select v-model="form.doorMagneticSensorStatus" placeholder="请选择" style="width: 200px">
                  <el-option v-for="item in sensorStatusList" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>


          <el-row :gutter="20">
            <el-col :span="12">


              <el-form-item

                label="拍照位置:"
                class="formItem"
                prop="photoLocation"
              >
                <el-select
                  v-model="form.photoLocation"
                  placeholder="请选择"
                  style="width: 200px"
                >
                  <el-option
                    v-for="item in photoLocationList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>


          </el-row>

          <el-row :gutter="20">

            <el-col :span="12">
              <el-form-item label="备注" >
                <el-input v-model="form.remark"  type="textarea" style="width: 600px;" />
              </el-form-item>
            </el-col>

          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;"
                @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55"/>
        <el-table-column prop="deviceId" label="ID"/>


        <el-table-column prop="onoffId" label="所属网络开关">
          <template slot-scope="scope">
            <!--   {{ scope.row.switchId  | getNetworkStateName(scope.row.switchId ,deviceSwitchList) }}-->
            {{ deviceOnoffMap[ scope.row.onoffId ] || '' }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceIp" label="设备ip"/>

        <el-table-column prop="deviceState" label="设备状态">
          <template slot-scope="scope">
          <span :class="getStatusColor(scope.row.deviceState)">
            {{ dict.label.device_state[scope.row.deviceState] }}
          </span>
          </template>
        </el-table-column>
        <el-table-column prop="networkState" label="网络状态">
          <template slot-scope="scope">

            <span :class="getNetworkStateColor(scope.row.networkState)">
            {{ dict.label.network_state[scope.row.networkState] }}
          </span>
          </template>
        </el-table-column>
        <el-table-column prop="lockingSensorStatus" label="环形传感器闭锁状态">
          <template slot-scope="scope">

            <span :class="getSensorStatusColor(scope.row.lockingSensorStatus)">
            {{ dict.label.sensor_status[scope.row.lockingSensorStatus] }}
          </span>
          </template>
        </el-table-column>


        <el-table-column prop="doorMagneticSensorStatus" label="门磁传感器闭锁状态">
          <template slot-scope="scope">

            <span :class="getSensorStatusColor(scope.row.doorMagneticSensorStatus)">
            {{ dict.label.sensor_status[scope.row.doorMagneticSensorStatus] }}
          </span>
          </template>
        </el-table-column>


   <!--     <el-table-column prop="deviceCode" label="设备编号"/>-->
        <el-table-column prop="deviceType" label="救援横通道">
          <template slot-scope="scope">

            {{ dict.label.rescue_cross_channel[scope.row.deviceType] }}

          </template>
        </el-table-column>
        <el-table-column prop="devicePosition" label="位置"/>


        <el-table-column prop="direction" label="上下行">
          <template slot-scope="scope">
            {{ dict.label.up_and_down[scope.row.direction] }}
          </template>
        </el-table-column>
        <el-table-column prop="photoLocation" label="拍照位置">
          <template slot-scope="scope">
            {{ dict.label.photo_location[scope.row.photoLocation] }}
          </template>
        </el-table-column>

        <el-table-column prop="remark" label="备注"/>
        <el-table-column prop="updateTime" label="更新时间"/>





        <el-table-column v-if="checkPer(['admin','device:edit','device:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination/>
    </div>
  </div>
</template>

<script>
  import crudDevice from '@/api/fhm/device'
  import {getALLDeviceOnoff} from '@/api/fhm/deviceOnoff'
  import CRUD, {presenter, header, form, crud} from '@crud/crud'
  import rrOperation from '@crud/RR.operation'
  import crudOperation from '@crud/CRUD.operation'
  import udOperation from '@crud/UD.operation'
  import pagination from '@crud/Pagination'
  import {findByType} from "@/api/system/dictDetail";

  const defaultForm = {
    deviceId: null,
    onoffId: null,
    deviceIp: null,
    deviceCode: null,
    deviceType: null,
    devicePosition: null,
    direction: null,
    createTime: null,
    updateTime: null,
    deleted: null,
    remark: null,
    photoLocation: null,
    networkState: null,
    deviceState: null,
    lockingSensorStatus: null,
    doorMagneticSensorStatus: null
  }
  export default {
    name: 'Device',
    components: {pagination, crudOperation, rrOperation, udOperation},
    mixins: [presenter(), header(), form(defaultForm), crud()],
    cruds() {
      return CRUD({
        title: '设备防护门',
        url: 'api/device',
        idField: 'deviceId',
        sort: 'deviceId,desc',
        crudMethod: {...crudDevice}
      })
    },
    // 数据字典
    dicts: ['network_state', 'device_state', 'photo_location', 'up_and_down', 'rescue_cross_channel','sensor_status'],
    data() {
      return {

        that: this,
        ipAddress: '',
        isValid: false,
        validationMessage: '',
        formLabelWidth: "120px",
        permission: {
          add: ['admin', 'device:add'],
          edit: ['admin', 'device:edit'],
          del: ['admin', 'device:del']
        },
        rules: {
          deviceIp: [
            { required: true, message: '设备ip不能为空', trigger: 'blur' }
          ],
          deviceType: [
            { required: true, message: '设备类型--是否救援横通道   0:不是，1是不能为空', trigger: 'blur' }
          ],
          devicePosition: [
            { required: true, message: '位置不能为空', trigger: 'blur' }
          ],
          direction: [
            { required: true, message: '上/下行---只有救援横通道行0：上，1下不能为空', trigger: 'blur' }
          ],
          photoLocation: [
            { required: true, message: '拍照位置：左侧/右侧不能为空', trigger: 'blur' }
          ],
          networkState: [
            { required: true, message: '网络状态：1稳定，2基本稳定，3轻微不稳定，4:严重不稳定不能为空', trigger: 'blur' }
          ],
          deviceState: [
            { required: true, message: '设备状态：1在线，2离线不能为空', trigger: 'blur' }
          ],
          lockingSensorStatus: [
            { required: true, message: '环形传感器闭锁状态不能为空', trigger: 'blur' }
          ],
          doorMagneticSensorStatus: [
            { required: true, message: '门磁传感器闭锁状态不能为空', trigger: 'blur' }
          ],
          onoffId: [
            { required: true, message: '所属网络开关不能为空', trigger: 'blur' }
          ]
        },
        queryTypeOptions: [
          {key: 'deviceId', display_name: 'ID'},
          {key: 'onoffId', display_name: '所属网络开关'},
          {key: 'deviceIp', display_name: '设备ip'},
          {key: 'deviceCode', display_name: '设备编号'},
          {key: 'deviceType', display_name: '设备类型--是否救援横通道   0:不是，1是'},
          {key: 'devicePosition', display_name: '位置'},
          {key: 'direction', display_name: '上/下行---只有救援横通道行0：上，1下'},
          {key: 'createTime', display_name: '创建日期'},
          {key: 'updateTime', display_name: '更新时间'},
          {key: 'deleted', display_name: '删除状态[0：未删除；1：已删除]'},
          {key: 'remark', display_name: '备注'},
          {key: 'photoLocation', display_name: '拍照位置：左侧/右侧'}
        ],
        networkList: [],  //网络状态
        deviceStateList: [], //设备状态
        deviceOnoffList: [], //开关
        photoLocationList: [], //安装位置
        upAnDownList: [], //上下行
        rescueCrossChannel: [], //是否救援横通道
        sensorStatusList: [], //开关状态

      }
    },
    mounted() {
      this.findByTypeNetworkList();
      this.findByTypeDeviceStateList();
      this.getALLDeviceOnoff();//获取 开关
      this.findPhotoLocationList();  //获取 拍照位置
      this.findUpAnDownList(); //上下行
      this.findRescueCrossChannel();//
      this.findSensorStatus();//开关状态
    },
    methods: {
      handleDeviceDetail(deviceIp) {

        // 使用若依的框架方法跳转路由并携带参数
        this.$router.push({
          name: 'deviceOnoffLineLog', // 对应若依路由配置中的name
          query: {
            deviceIp: deviceIp
          }

        });


      },
      getStatusColor(state) {
        // 根据设备状态返回对应的颜色类
        // 这里需要根据你的实际状态值和颜色需求进行调整
        switch (state) {
          case '1': // 假设在线状态值为'online'
            return 'text-green';
          case '2': // 假设离线状态值为'offline'
            return 'text-red';
        }
      },
      getSensorStatusColor(state) {
        // 根据设备状态返回对应的颜色类
        // 这里需要根据你的实际状态值和颜色需求进行调整
        switch (state) {
          case '0': //
            return 'text-green';
          case '1': //
            return 'text-red';

        }
      },

      getNetworkStateColor(state) {
        // 根据设备状态返回对应的颜色类
        // 这里需要根据你的实际状态值和颜色需求进行调整
        switch (state) {
          case '1': //
            return 'text-green';
          case '2': //
            return 'text-green';
          case '3': //
            return 'text-yellow';
          case '4': //
            return 'text-red';
        }
      },
      validateIP() {
        debugger
        // 清除之前的验证消息
        this.validationMessage = '';

        // 如果输入为空
        if (!this.form.deviceIp.trim()) {


          this.$message({
            type: "error",
            message: "IP地址不能为空",
          });
          return;
        }

        // 正则表达式验证IPv4格式
        const ipv4Regex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

        if (ipv4Regex.test(this.form.deviceIp)) {
          this.validationMessage = 'IP地址格式正确';
        } else {
          this.form.deviceIp = '';
          this.$message({
            type: "error",
            message: "请输入有效的IPv4地址 (例如: ***********)",
          });

        }
      },
      //通信状态
      findByTypeNetworkList() {
        const param = {
          dictName: "network_state",
        };
        findByType(param).then((res) => {
          this.networkList = res;
        });
      },
      findByTypeDeviceStateList() {
        const param = {
          dictName: "device_state",
        };
        findByType(param).then((res) => {
          this.deviceStateList = res;
        });
      },
      //安装位置
      findPhotoLocationList() {
        this.photoLocationList = this.dict.photo_location
      },

      //上下行
      findUpAnDownList() {
        this.upAnDownList = this.dict.up_and_down
      },
      //是否救援横通道
      findRescueCrossChannel() {
        this.rescueCrossChannel = this.dict.rescue_cross_channel
      },

      //开关状态
      findSensorStatus() {
        this.sensorStatusList = this.dict.sensor_status
      },




      //获取开关列表
      getALLDeviceOnoff() {
        const param = {};
        getALLDeviceOnoff(param).then((res) => {
          this.deviceOnoffList = res;
          debugger
        });


      }
      ,
      // 钩子：在获取表格数据之前执行，false 则代表不获取数据
      [CRUD.HOOK.beforeRefresh]() {
        return true
      }

    },
    computed: {

      //返回 所属开关  ip
      deviceOnoffMap(onoffId) {
        debugger
        return this.deviceOnoffList.reduce((map, item) => {
          map[item.deviceId] = item.deviceIp;
          return map;
        }, {});
      }
    },
  }
</script>

<style scoped>
  .text-green {
    color: green;
  }

  .text-red {
    color: red;
  }

  .text-yellow {
    color: yellow;
  }
</style>
