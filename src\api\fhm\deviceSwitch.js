import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/deviceSwitch',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/deviceSwitch/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/deviceSwitch',
    method: 'put',
    data
  })
}
export function getALLDeviceSwitch(data) {
  return request({
    url: 'api/deviceSwitch/getALLDeviceSwitch',
    method: 'post',
    data
  })
}
export default { add, edit, del,getALLDeviceSwitch }
