<template>
  <div class="dashboard-container">
    <div class="echart-container">
      <div id="chart-panel" :style="{height:viewportHeight,width:width}"></div>

    </div>

  </div>
</template>

<script>
  // eslint-disable-next-line no-unused-vars
  import echarts from 'echarts'
  import {queryDeviceSwitch} from '@/api/fhm/deviceShowApi'

  require('echarts/theme/macarons') // echarts theme
  import request from '@/utils/request'
  import fhm from './dashboard/fhm'
  import resize from './dashboard/mixins/resize'

  export default {
    mixins: [resize],
    name: 'Dashboard',
    components: {
      // eslint-disable-next-line vue/no-unused-components
      fhm

    },
    data() {
      return {
        idata:[],
        jdata: [],
        links: [],
        arrDevice: [],
        viewportHeight: window.innerHeight,// 初始设置为当前窗口高度
        width: '100%',
        height: '800px',
        // 颜色
        colors: [
          '#3CB371'
        ]
      }
    },
    mounted() {


      this.updateViewportHeight();
      window.addEventListener('resize', this.updateViewportHeight);
      this.$nextTick(() => {
        this.getData()
      })


    },
    beforeDestroy() {
      window.removeEventListener('resize', this.updateViewportHeight);
    },
    methods: {


      updateViewportHeight() {
        this.viewportHeight = window.innerHeight - 100 + 'px';
      },
      /*      getData() {
             const params = {};
             const res =  queryDeviceSwitch(params);
             let arr = [];
             arr.push(res)
             arr = this.handleData(arr, 0)
             this.arrDevice = arr;

             return arr;
           },*/



      getData() {
        const params = {};
        let arr = [];
        // 使用request调用API，但不显示loading状态
        request({
          url: 'api/deviceApi/queryDeviceSwitch',
          method: 'get',
          params: params
        }).then(res => {
          arr.push(res)
          arr = this.handleData(arr, 0)

          this.arrDevice = arr;
          this.dataChange(arr)
          this.initChart(arr)

        }).catch(error => {
          console.error('静默获取历史数据失败:', error);
          // 静默失败，不显示错误消息
        });
        return arr;
      },




      // 获取所有数据
      /*   getData() {
              const data = {
                name: '升压站',
                value: "1",
                id: 0,
                children: []
              }
              for (let i = 1; i <= 19; i++) {
                const obj = {
                  name: i + '#箱变',
                  value: i+"swewe",
                  id: 0,
                  children: []
                }
                for (let j = 1; j <= 8; j++) {
                  const obj2 = {
                    id: 0,
                    name: `逆变器-${i}-${j}`,
                    value: 1 + '-' + i + '-' + j
                  }
                  // eslint-disable-next-line eqeqeq
                  if (j % 2 == 1) {
                    obj2.children = []
                    for (let k = 1; k <= 2; k++) {
                      const obj3 = {
                        id: 0,
                        name: `组串-${i}-${j}-${k}`,
                        value: 1 + '-' + i + '-' + j + '-' + k
                      }
                      obj2.children.push(obj3)
                    }
                  }

                  obj.children.push(obj2)
                }

                data.children.push(obj)
              }
              let arr = []
              arr.push(data)
              console.log(JSON.stringify(arr))


              arr = this.handleData(arr, 0)

              return arr
            },*/

      // 数据颜色  组装
      handleData(data, index, color = '#3CB371') {

        // index标识第几层
        return data.map((item, index2) => {
          // 计算出颜色
          // eslint-disable-next-line eqeqeq
          if (index == 1) {
            // eslint-disable-next-line no-undef,eqeqeq
            //  color = this.colors.find((item, eq) => eq == index2 % 5)
            color = "#3CB371"
          }
          // 设置节点大小
          if (index === 0 || index === 1) {
            item.label = {
              position: 'inside'
              //   rotate: 0,
              //   borderRadius: "50%",
            }
          }
          // 设置label大小
          switch (index) {
            case 0:
              item.symbolSize = 70
              break
            case 1:
              item.symbolSize = 20
              break
            default:
              item.symbolSize = 10
              break
          }
          // 设置线条颜色
          item.lineStyle = {
            color: color
          }

          if (item.children) { // 存在子节点
            item.itemStyle = {
              borderColor: '#3CB371',
              color: '#3CB371'
            }

            if (index2 == 1) {
              item.children = this.handleData(item.children, index + 1, "red")
            } else {
              item.children = this.handleData(item.children, index + 1, color)
            }

          } else { // 不存在
            item.itemStyle = {
              color: '#3CB371',
              borderColor: '#3CB371'
            }
          }
          return item
        })
      },

      dataChange(data) {

        var idata = data[0];
        var jdata = data[0];



        var ichildren = []
        var jchildren = []
        for (let i = 0; i < data[0].children.length; i++) {
          var jtem = data[0].children[i]
          if (i <= 9) {
            ichildren.push(jtem)
          } else {
            jchildren.push(jtem)
          }
        }
        idata.children=[]
        idata.children = ichildren
        console.log( idata.children.length)
        this.idata.push(idata)
        jdata.children=[]
        jdata.children = jchildren
        console.log( jdata.children.length)
        this.jdata.push(jdata)

        debugger
      },

      initChart(data) {
        debugger
        // this.chart = echarts.init(this.$el, 'macarons')
        var dom = document.getElementById("chart-panel");
        this.chart = echarts.init(dom);


        const jsonString = JSON.stringify(data);
        console.log(jsonString)
        debugger

        const option = {
          type: 'tree',
          backgroundColor: '#000',
          toolbox: { // 工具栏
            show: true,
            iconStyle: {
              borderColor: '#03ceda'
            },
            feature: {
              restore: {}
            }
          },
          tooltip: { // 提示框
            trigger: 'item',
            triggerOn: 'mousemove',
            backgroundColor: 'rgba(1,70,86,1)',
            borderColor: 'rgba(0,246,255,1)',
            borderWidth: 0.5,
            textStyle: {
              fontSize: 10
            }
          },
          /*
                    symbol: 'rect', // 节点标记形状
                    edgeShape: 'polyline', //设置连接线曲线还是折线，默认情况下是曲线，curve曲线 polyline直线
                    orient: 'vertical', //树整体的方向horizontal横向 vertical竖向*/
          series: [{
            type: 'tree',
            name: 'hhh',
            data: this.idata,
            top: '5%',
            height: '40%',
            symbolSize: 8,
            initialTreeDepth: 6,
            orient: 'BT',
            links: this.links,  // 应用自定义连接线
            label: {},
            leaves: {
              label: {
                normal: {
                  position: 'bottom',
                  verticalAlign: 'middle',
                  align: 'left',
                }
              }
            },
            expandAndCollapse: false,
            animationDuration: 550,
            animationDurationUpdate: 750
          }, {
            type: 'tree',
            name: 'hhh65',
            data: this.jdata,
            top: '45%',
            orient: 'TB',
            height: '40%',
            symbolSize: 8,
            initialTreeDepth: 6,
            label: {},
            leaves: {
              label: {
                normal: {
                  position: 'top',
                  verticalAlign: 'middle',
                  align: 'left',
                }
              }
            },
            expandAndCollapse: false,
            animationDuration: 550,
            animationDurationUpdate: 750
          }]
        };
        this.chart.setOption(option)
        /* if (option && typeof option === 'object') {
           myChart.setOption(option)
         }*/
      }

    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>
