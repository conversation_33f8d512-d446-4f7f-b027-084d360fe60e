<template>
  <div class="dashboard-container">
    <div class="echart-container">
      <div id="chart-panel" :style="{height:viewportHeight,width:width}"></div>

    </div>

  </div>
</template>

<script>
  // eslint-disable-next-line no-unused-vars
  import echarts from 'echarts'
  import {queryDeviceSwitch} from '@/api/fhm/deviceShowApi'

  require('echarts/theme/macarons') // echarts theme
  import request from '@/utils/request'
  import fhm from './dashboard/fhm'
  import resize from './dashboard/mixins/resize'

  export default {
    mixins: [resize],
    name: 'Dashboard',
    components: {
      // eslint-disable-next-line vue/no-unused-components
      fhm

    },
    data() {
      return {
        arrDevice : [
          {
            name: '根节点',
            type: 'root',
            children: [
              {
                name: '子节点1',
                type: 'sub',
                children: [
                  {
                    name: '叶子节点1',
                    type: 'sub',
                    children: [
                      {
                        name: '叶叶节点1',
                        type: 'sub',
                        children: [
                          {
                            name: '叶叶叶节点1',
                            type: 'final',
                            children: [],
                          },
                          {
                            name: '叶叶叶节点2',
                            type: 'sub',
                            children: [],
                          },
                        ],
                      },
                      {
                        name: '叶叶节点2',
                        type: 'sub',
                        children: [],
                      },
                      {
                        name: '叶叶节点3',
                        type: 'sub',
                        children: [],
                      },
                    ],
                  },
                  {
                    name: '叶子节点2',
                    children: [],
                  },
                  {
                    name: '叶子节点3',
                    children: [],
                  },
                ],
              },
              {
                name: '子节点2',
                type: 'sub',
                children: [
                  {
                    name: '叶子节点4',
                    type: 'sub',
                    children: [],
                  },
                  {
                    name: '叶子节点5',
                    type: 'final',
                    children: [],
                  },
                  {
                    name: '叶子节点6',
                    type: 'sub',
                    children: [],
                  },
                ],
              },
            ],
          },
        ],

        viewportHeight: window.innerHeight,// 初始设置为当前窗口高度
        width: '100%',
        height: '800px',
        // 颜色
        colors: [
          '#3CB371'
        ]
      }
    },
    mounted() {


      this.updateViewportHeight();
      window.addEventListener('resize', this.updateViewportHeight);
      this.$nextTick(() => {
        this.getData()
      })


    },
    beforeDestroy() {
      window.removeEventListener('resize', this.updateViewportHeight);
    },
    methods: {


      updateViewportHeight() {
        this.viewportHeight = window.innerHeight - 100 + 'px';
      },
 /*      getData() {
        const params = {};
        const res =  queryDeviceSwitch(params);
        let arr = [];
        arr.push(res)
        arr = this.handleData(arr, 0)
        this.arrDevice = arr;

        return arr;
      },*/




     getData() {
     const params = {

        };
        let arr = [];
        // 使用request调用API，但不显示loading状态
        request({
          url: 'api/deviceApi/queryDeviceSwitch',
          method: 'get',
          params: params
        }).then(res => {
          arr.push(res)

          const jsonString = JSON.stringify(arr);
          console.log(jsonString)
          arr = this.recursionFun(arr)

          this.arrDevice = arr;
          this.initChart(arr)
        }).catch(error => {
          console.error('静默获取历史数据失败:', error);
          // 静默失败，不显示错误消息
        });
        return arr;

      },




   /*   getData(){
        var dd=this.arrDevice;
        const jsonString = JSON.stringify(dd);
        console.log(jsonString)

        var  data=  this.recursionFun(this.arrDevice)
        debugger
        this.initChart(data);
      },
*/



    recursionFun(data) {
    for (let i = 0; i < data.length; i++) {
      // 根据type标识符判断当前阶段是否需要竖向
      if (data[i].type == 'final') {
        data[i].name = data[i].name.split('').join('\n') // 每个字换行
        data[i].symbolSize = [30, 95];
      }
      if (data[i].children.length > 0) {
        this.recursionFun(data[i].children);
      }
    }
    return data;
  },

      initChart(data) {
        // this.chart = echarts.init(this.$el, 'macarons')
        var dom = document.getElementById("chart-panel");
        this.chart = echarts.init(dom);


        debugger
      var  option = {
          tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove',
          },
          series: [
            {
              type: 'tree',
              data: this.recursionFun(this.arrDevice),
              initialTreeDepth: 100, //默认树展开的层数
              left: '2%',
              right: '2%',
              top: '8%',
              bottom: '20%',
              symbolSize: [90, 30], //设置框的大小
              symbol: 'rect', // 节点标记形状
              edgeShape: 'polyline', //设置连接线曲线还是折线，默认情况下是曲线，curve曲线 polyline直线
              orient: 'vertical', //树整体的方向horizontal横向 vertical竖向
              expandAndCollapse: true,
              itemStyle: {
                color: '#fff',
                borderColor: '#333',
                borderWidth: 0.1,
                overflow: 'truncate',
              },
              //lable 设置含有子节点的样式
              label: {
                show: true,
                position: 'inside',
                textStyle: {
                  fontSize: 15,
                  color: '#333',
                },
                verticalAlign: 'middle',
                align: 'center',
                height: 290,
                width: 210,
              },
              leaves: {
                // 设置末节点的样式
                label: {
                  position: 'inside',
                  color: 'red',
                  verticalAlign: 'middle',
                  align: 'center',
                  height: 290,
                  width: 100,
                },
              },
              lineStyle: {
                color: '#7b7b7b', //连接线的颜色
                width: 1,
              },
              animationDurationUpdate: 750,
            },
          ],
        };


        this.chart.setOption(option)
        /* if (option && typeof option === 'object') {
           myChart.setOption(option)
         }*/
      }

    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>
