<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->

        <label class="el-form-item-label">设备IP</label>
        <el-input v-model="query.deviceIp" clearable placeholder="设备ip" style="width: 120px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">设备编号</label>
        <el-input v-model="query.deviceCode" clearable placeholder="设备编号" style="width: 120px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">位置</label>
        <el-input v-model="query.devicePosition" clearable placeholder="位置" style="width: 120px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>


        <label class="el-form-item-label">设备状态</label>


        <el-select
          v-model="query.deviceState"
          placeholder="请选择"
          style="width: 120px;" class="filter-item"
        >
          <el-option
            v-for="item in deviceStateList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <label class="el-form-item-label">所属开关</label>
        <el-select
          v-model="form.type"
          placeholder="请选择"
          style="width: 120px"

        >
          <el-option
            v-for="item in quantityType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <label class="el-form-item-label">网络状态</label>
        <el-select
          v-model="query.networkState"
          placeholder="请选择"
          style="width: 120px;" class="filter-item"
        >
          <el-option
            v-for="item in networkList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>

        <rrOperation :crud="crud"/>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="600px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">

          <el-form-item label="设备ip" :label-width="formLabelWidth" prop="deviceIp">
            <el-input v-model="form.deviceIp" @change="validateIP" style="width: 370px;" maxlength="20"/>
          </el-form-item>
    <!--      <el-form-item label="设备编号" :label-width="formLabelWidth" prop="deviceCode">
            <el-input v-model="form.deviceCode" style="width: 370px;" maxlength="20"/>
          </el-form-item>-->
          <el-form-item label="位置" :label-width="formLabelWidth" prop="devicePosition">
            <el-input v-model="form.devicePosition" style="width: 370px;" maxlength="20"/>
          </el-form-item>





          <el-form-item
            :label-width="formLabelWidth"
            label="设备状态:"
            class="formItem"
            prop="deviceState"
          >
            <el-select
              v-model="form.deviceState"
              placeholder="请选择"
              style="width: 250px"
            >
              <el-option
                v-for="item in deviceStateList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>


          <el-form-item
            label="网络状态:"
            class="formItem"

            prop="networkState"
            :label-width="formLabelWidth"
          >
            <el-select
              v-model="form.networkState"
              placeholder="请选择"
              style="width: 250px"
            >
              <el-option
                v-for="item in networkList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            :label-width="formLabelWidth"
            label="所属交换机:"
            class="formItem"
            prop="switchId"
          >
            <el-select
              v-model="form.switchId"
              placeholder="请选择"
              style="width: 250px"
            >
              <el-option
                v-for="item in deviceSwitchList"
                :key="item.deviceIp"
                :label="item.deviceIp"
                :value="item.deviceId"
              >
              </el-option>
            </el-select>
          </el-form-item>




          <el-form-item
            :label-width="formLabelWidth"
            label="关联设备数量:"
            class="formItem"
            prop="type"
          >
            <el-select
              v-model="form.type"
              placeholder="请选择"
              style="width: 250px"
            >
              <el-option
                v-for="item in quantityType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

 <!--         <el-form-item label="排序" :label-width="formLabelWidth" prop="sort">
            <el-input v-model="form.sort" style="width: 370px;" type="number" maxlength="20"/>
          </el-form-item>-->

          <el-form-item label="备注">
            <el-input type="textarea" v-model="form.remark" style="width: 370px;" maxlength="2000"/>
          </el-form-item>



        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="deviceIp" label="设备IP">
          <template slot-scope="scope">

            <span   @click="handleDeviceDetail(scope.row.deviceIp)">{{ scope.row.deviceIp }}</span>

          </template>
        </el-table-column>
      <!--  <el-table-column prop="deviceCode" label="设备编号"/>-->
        <el-table-column prop="devicePosition" label="位置"/>
        <el-table-column prop="deviceState" label="设备状态">
          <template slot-scope="scope">
          <span :class="getStatusColor(scope.row.deviceState)">
            {{ dict.label.device_state[scope.row.deviceState] }}
          </span>
          </template>
        </el-table-column>
        <el-table-column prop="networkState" label="网络状态">
          <template slot-scope="scope">

            <span :class="getNetworkStateColor(scope.row.networkState)">
            {{ dict.label.network_state[scope.row.networkState] }}
          </span>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="开关" >
        <template slot-scope="scope">

            <span :class="getNetworkStateColor(scope.row.type)">
            {{ dict.label.quantity_type[scope.row.type] }}
          </span>
        </template>
        </el-table-column>


        <el-form-item
          :label-width="formLabelWidth"
          label="所属交换机:"
          class="formItem"
          prop="switchId"
        >
          <el-select
            v-model="form.switchId"
            placeholder="请选择"
            style="width: 250px"
          >
            <el-option
              v-for="item in deviceSwitchList"
              :key="item.deviceIp"
              :label="item.deviceIp"
              :value="item.deviceIp"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-table-column prop="switchId" label="所属交换机" >
        <template slot-scope="scope">
       <!--   {{ scope.row.switchId  | getNetworkStateName(scope.row.switchId ,deviceSwitchList) }}-->
      {{ switchIdMap[ scope.row.switchId ] || '' }}
        </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间"/>
        <el-table-column v-if="checkPer(['admin','deviceOnoff:edit','deviceOnoff:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
  import crudDeviceOnoff from '@/api/fhm/deviceOnoff'
  import { getALLDeviceSwitch } from '@/api/fhm/deviceSwitch'
  import CRUD, { presenter, header, form, crud } from '@crud/crud'
  import rrOperation from '@crud/RR.operation'
  import crudOperation from '@crud/CRUD.operation'
  import udOperation from '@crud/UD.operation'
  import pagination from '@crud/Pagination'
  import {findByType} from "@/api/system/dictDetail";
  const defaultForm = { deviceId: null, switchId: null, deviceIp: null, deviceCode: null, createTime: null, updateTime: null, deleted: null, remark: null, type: null, deviceState: null, devicePosition: null, networkState: null }
  export default {
    name: 'DeviceOnoff',
    components: { pagination, crudOperation, rrOperation, udOperation },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    cruds() {
      return CRUD({ title: '设备开关', url: 'api/deviceOnoff', idField: 'deviceId', sort: 'deviceId,desc', crudMethod: { ...crudDeviceOnoff }})
    },
    // 数据字典
    dicts: ['network_state', 'device_state','quantity_type'],
    data() {
      return {


        that:this,
        ipAddress: '',
        isValid: false,
        validationMessage: '',
        formLabelWidth: "120px",
        permission: {
          add: ['admin', 'deviceOnoff:add'],
          edit: ['admin', 'deviceOnoff:edit'],
          del: ['admin', 'deviceOnoff:del']
        },
        rules: {
          switchId: [
            { required: true, message: '所属交换机不能为空', trigger: 'blur' }
          ],
          deviceIp: [
            { required: true, message: '设备ip不能为空', trigger: 'blur' }
          ],
          deviceCode: [
            { required: true, message: '设备编号不能为空', trigger: 'blur' }
          ],
          type: [
            { required: true, message: '开关数量不能为空', trigger: 'blur' }
          ],
          deviceState: [
            { required: true, message: '设备状态不能为空', trigger: 'blur' }
          ],
          devicePosition: [
            { required: true, message: '设备位置不能为空', trigger: 'blur' }
          ]
          ,
          networkState: [
            { required: true, message: '网络状态不能为空', trigger: 'blur' }
          ]




        },
        queryTypeOptions: [
          { key: 'deviceId', display_name: 'ID' },
          { key: 'switchId', display_name: '所属交换机' },
          { key: 'deviceIp', display_name: '设备ip' },
          { key: 'deviceCode', display_name: '设备编号' },
          { key: 'createTime', display_name: '创建日期' },
          { key: 'updateTime', display_name: '更新时间' },
          { key: 'deleted', display_name: '删除状态[0：未删除；1：已删除]' },
          { key: 'remark', display_name: '备注' },
          { key: 'type', display_name: '开关：0:1个设备，1：多个设备' },
          { key: 'deviceState', display_name: '设备状态：1在线，2离线' },
          { key: 'devicePosition', display_name: '设备位置--里程' },
          { key: 'networkState', display_name: '网络状态：1稳定，2基本稳定，3轻微不稳定，4:严重不稳定' }
        ],
        networkList: [],
        deviceStateList: [],
        deviceSwitchList : [],
        quantityType : []
      }
    },
    mounted() {
      this.findByTypeNetworkList();
      this.findByTypeDeviceStateList();
      this.getALLDeviceSwitch();//获取 交换机
      this.findQuantityTypeList();
    },
    methods: {
      handleDeviceDetail(deviceIp){

        // 使用若依的框架方法跳转路由并携带参数
        this.$router.push({
          name: 'deviceOnoffLineLog', // 对应若依路由配置中的name
          query: {
            deviceIp: deviceIp
          }

        });


      },
      getStatusColor(state) {
        // 根据设备状态返回对应的颜色类
        // 这里需要根据你的实际状态值和颜色需求进行调整
        switch (state) {
          case '1': // 假设在线状态值为'online'
            return 'text-green';
          case '2': // 假设离线状态值为'offline'
            return 'text-red';
        }
      },
      getNetworkStateColor(state) {
        // 根据设备状态返回对应的颜色类
        // 这里需要根据你的实际状态值和颜色需求进行调整
        switch (state) {
          case '1': //
            return 'text-green';
          case '2': //
            return 'text-green';
          case '3': //
            return 'text-yellow';
          case '4': //
            return 'text-red';
        }
      },
      validateIP() {
        debugger
        // 清除之前的验证消息
        this.validationMessage = '';

        // 如果输入为空
        if (!this.form.deviceIp.trim()) {


          this.$message({
            type: "error",
            message: "IP地址不能为空",
          });
          return;
        }

        // 正则表达式验证IPv4格式
        const ipv4Regex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

        if (ipv4Regex.test(this.form.deviceIp)) {
          this.validationMessage = 'IP地址格式正确';
        } else {
          this.form.deviceIp = '';
          this.$message({
            type: "error",
            message: "请输入有效的IPv4地址 (例如: ***********)",
          });

        }
      },
      //通信状态
      findByTypeNetworkList() {
        const param = {
          dictName: "network_state",
        };
        findByType(param).then((res) => {
          this.networkList = res;
        });
      },
      findByTypeDeviceStateList() {
        const param = {
          dictName: "device_state",
        };
        findByType(param).then((res) => {
          this.deviceStateList = res;
        });
      },
      //获取 开关 关联  设备类型
      findQuantityTypeList() {
        this.quantityType = this.dict.quantity_type
      },
      //获取交换机
      getALLDeviceSwitch(){
        const param = {
        };
        getALLDeviceSwitch(param).then((res) => {
          this.deviceSwitchList = res;
        });


      }
      ,
      // 钩子：在获取表格数据之前执行，false 则代表不获取数据
      [CRUD.HOOK.beforeRefresh]() {
        return true
      }
    },
    computed: {

      //返回 所属交换机 ip
      switchIdMap() {
        return this.deviceSwitchList.reduce((map, item) => {
          debugger
          map[item.deviceId] = item.deviceIp;
          return map;
        }, {});
      }


    },
    filters: {


    }
  }
</script>

<style scoped>
  .text-green{
    color:green;
  }
  .text-red{
    color: red;
  }
  .text-yellow{
    color: yellow;
  }

</style>
